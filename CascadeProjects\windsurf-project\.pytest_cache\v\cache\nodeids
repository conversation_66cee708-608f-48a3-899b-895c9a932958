["tests/test_base_client.py::TestBaseDiscoveryClient::test_enforce_rate_limit", "tests/test_base_client.py::TestBaseDiscoveryClient::test_fetch_all_pages", "tests/test_base_client.py::TestBaseDiscoveryClient::test_make_request_max_retries_exceeded", "tests/test_base_client.py::TestBaseDiscoveryClient::test_make_request_retry", "tests/test_base_client.py::TestBaseDiscoveryClient::test_make_request_success", "tests/test_base_client.py::TestBaseDiscoveryClient::test_stream_dataclass", "tests/test_twitch_client.py::TestTwitchDiscovery::test_api_error_handling", "tests/test_twitch_client.py::TestTwitchDiscovery::test_fetch_live_streams", "tests/test_twitch_client.py::TestTwitchDiscovery::test_get_game_info", "tests/test_twitch_client.py::TestTwitchDiscovery::test_init", "tests/test_twitch_client.py::TestTwitchDiscovery::test_parse_datetime", "tests/test_twitch_client.py::TestTwitchDiscovery::test_search_channels", "tests/test_youtube_client.py::TestYouTubeDiscovery::test_api_error_handling", "tests/test_youtube_client.py::TestYouTubeDiscovery::test_fetch_all_pages", "tests/test_youtube_client.py::TestYouTubeDiscovery::test_fetch_live_streams", "tests/test_youtube_client.py::TestYouTubeDiscovery::test_get_thumbnail", "tests/test_youtube_client.py::TestYouTubeDiscovery::test_init", "tests/test_youtube_client.py::TestYouTubeDiscovery::test_parse_datetime"]